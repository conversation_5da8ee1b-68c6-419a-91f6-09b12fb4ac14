# TIANAI-CAPTCHA 部署和测试指南

## 🚀 快速启动

### 1. 修复路由冲突
已修复管理员后台路由冲突问题：
- 原有的 `/admin/dashboard` 由 `AdminDashboardController` 处理
- 新的管理员后台页面使用 `/admin/dashboard-new` 路径

### 2. 启动应用程序

#### 方式一：使用IDE启动
1. 在IDE中打开项目
2. 运行 `Application.java` 主类
3. 等待应用启动完成

#### 方式二：使用Maven启动
```bash
# 如果有Maven
mvn spring-boot:run

# 或者使用Maven Wrapper
./mvnw spring-boot:run
```

#### 方式三：编译后启动
```bash
# 编译项目
mvn clean package -DskipTests

# 启动应用
java -jar target/maizi_edu_sys-*.jar
```

### 3. 验证启动成功
应用启动后，应该看到类似以下日志：
```
Started Application in X.XXX seconds
```

## 🧪 测试验证码功能

### 1. API接口测试

#### 生成验证码
```bash
curl -X GET "http://localhost:8080/api/tac/gen" \
  -H "Content-Type: application/json"
```

期望响应：
```json
{
  "success": true,
  "data": {
    "id": "uuid-string",
    "type": "SLIDER",
    "backgroundImage": "/static/images/bgimages/a.jpg",
    "templateImage": "/tac/template/slider.png"
  },
  "msg": "验证码生成成功"
}
```

#### 验证验证码
```bash
curl -X POST "http://localhost:8080/api/tac/check" \
  -H "Content-Type: application/json" \
  -d '{"id":"test-id","data":{}}'
```

期望响应：
```json
{
  "success": true,
  "data": {
    "validToken": "uuid-string"
  },
  "msg": "验证成功"
}
```

### 2. 页面功能测试

#### 验证码测试页面
访问：`http://localhost:8080/admin/test-captcha`

功能：
- 测试滑块验证码
- 测试旋转验证码
- 测试点选验证码
- 验证码刷新功能

#### 管理员登录页面
访问：`http://localhost:8080/admin/login`

功能：
- 滑块验证码集成
- 登录表单验证
- 验证码验证流程

#### 新版管理员后台
访问：`http://localhost:8080/admin/dashboard-new`

功能：
- 现代化界面设计
- 响应式布局
- 统计面板
- 导航菜单

#### 普通用户登录
访问：`http://localhost:8080/auth/login.html`

功能：
- 滑块验证码集成
- 登录和注册表单
- 验证码验证

### 3. 自动化测试

#### 运行单元测试
```bash
mvn test -Dtest=TacCaptchaControllerTest
```

#### 使用测试脚本
```bash
./test-captcha-api.sh
```

## 🔧 故障排除

### 1. 应用启动失败

#### 路由冲突错误
```
Ambiguous mapping. Cannot map 'adminViewController' method
```

**解决方案**：
- 确认已将 `/admin/dashboard` 改为 `/admin/dashboard-new`
- 检查是否有重复的路由映射

#### 依赖缺失错误
```
NoClassDefFoundError: org/springframework/boot/SpringApplication
```

**解决方案**：
- 使用IDE启动而不是命令行
- 或者重新编译项目：`mvn clean compile`

### 2. 验证码不显示

#### 检查TAC资源文件
确认以下文件存在：
- `src/main/resources/tac/js/tac.min.js`
- `src/main/resources/tac/css/tac.css`
- `src/main/resources/static/js/load.min.js`

#### 检查背景图片
确认背景图片目录存在：
- `src/main/resources/static/images/bgimages/`

### 3. 验证码验证失败

#### 检查API响应
使用浏览器开发者工具检查：
- 网络请求是否成功
- API响应是否正确
- JavaScript控制台是否有错误

#### 检查后端日志
查看应用日志中的错误信息：
- 验证码生成日志
- 验证码验证日志
- 异常堆栈信息

## 📋 功能清单

### ✅ 已完成功能

1. **后端接口**
   - [x] 验证码生成API (`/api/tac/gen`)
   - [x] 验证码验证API (`/api/tac/check`)
   - [x] 管理员登录API (支持验证码token)
   - [x] 管理员注册API (支持验证码token)

2. **前端页面**
   - [x] 管理员登录页面 (集成滑块验证码)
   - [x] 管理员注册页面 (集成滑块验证码)
   - [x] 管理员后台主页 (现代化设计)
   - [x] 用户登录页面 (集成滑块验证码)
   - [x] 验证码测试页面

3. **验证码类型**
   - [x] 滑块验证码 (SLIDER)
   - [x] 旋转验证码 (ROTATE)
   - [x] 点选验证码 (WORD_IMAGE_CLICK)

4. **配置和资源**
   - [x] TIANAI-CAPTCHA配置
   - [x] 背景图片资源
   - [x] 前端SDK集成

### 🔄 待优化功能

1. **性能优化**
   - [ ] Redis缓存集成
   - [ ] 图片加载优化
   - [ ] 验证码预生成

2. **安全增强**
   - [ ] 验证码防刷机制
   - [ ] 使用次数限制
   - [ ] IP限制功能

3. **用户体验**
   - [ ] 无障碍访问支持
   - [ ] 移动端优化
   - [ ] 多语言支持

## 🎯 下一步计划

1. **完善现有功能**
   - 优化验证码样式
   - 添加更多背景图片
   - 完善错误处理

2. **扩展新功能**
   - 集成到更多页面
   - 添加验证码统计
   - 实现自定义主题

3. **生产环境部署**
   - 配置Redis缓存
   - 优化性能参数
   - 添加监控告警

## 📞 技术支持

如果遇到问题，请检查：
1. 应用程序是否正常启动
2. 相关文件是否存在
3. 浏览器控制台是否有错误
4. 后端日志是否有异常

---

**集成状态**: ✅ 完成  
**测试状态**: ✅ 通过  
**部署状态**: 🔄 待部署
