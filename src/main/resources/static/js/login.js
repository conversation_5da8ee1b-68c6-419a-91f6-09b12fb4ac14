// 全局变量
let tacInstance = null;
let captchaValidToken = null;
let registerTacInstance = null;
let registerCaptchaValidToken = null;

document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const showRegister = document.getElementById('showRegister');
    const showLogin = document.getElementById('showLogin');
    const formTitle = document.getElementById('formTitle');

    // 初始化TIANAI-CAPTCHA验证码
    initTacCaptcha();
    initRegisterTacCaptcha();

    // 初始化TIANAI-CAPTCHA验证码
    function initTacCaptcha() {
        const config = {
            // 生成接口
            requestCaptchaDataUrl: "/api/tac/gen",
            // 验证接口
            validCaptchaUrl: "/api/tac/check",
            // 验证码绑定的div块
            bindEl: "#user-captcha-box",
            // 验证成功回调函数
            validSuccess: (res, c, tac) => {
                console.log("验证码验证成功", res);
                captchaValidToken = res.data.validToken;
                showToast('验证码验证成功', 'success');
            },
            // 验证失败的回调函数
            validFail: (res, c, tac) => {
                console.log("验证码验证失败", res);
                captchaValidToken = null;
                showToast('验证码验证失败，请重试', 'error');
                // 验证失败后重新拉取验证码
                tac.reloadCaptcha();
            },
            // 刷新按钮回调事件
            btnRefreshFun: (el, tac) => {
                console.log("刷新验证码");
                captchaValidToken = null;
                tac.reloadCaptcha();
            },
            // 关闭按钮回调事件
            btnCloseFun: (el, tac) => {
                console.log("关闭验证码");
                tac.destroyWindow();
            }
        };

        // 样式配置
        let style = {
            logoUrl: null // 去除logo
        };

        // 初始化TAC验证码
        window.initTAC("/tac", config, style).then(tac => {
            tacInstance = tac;
            tac.init(); // 显示验证码
        }).catch(e => {
            console.log("初始化验证码失败", e);
            showToast("验证码初始化失败，请刷新页面重试", 'error');
        });
    }

    // 初始化注册页面的TIANAI-CAPTCHA验证码
    function initRegisterTacCaptcha() {
        const config = {
            // 生成接口
            requestCaptchaDataUrl: "/api/tac/gen",
            // 验证接口
            validCaptchaUrl: "/api/tac/check",
            // 验证码绑定的div块
            bindEl: "#register-captcha-box",
            // 验证成功回调函数
            validSuccess: (res, c, tac) => {
                console.log("注册验证码验证成功", res);
                registerCaptchaValidToken = res.data.validToken;
                showToast('验证码验证成功', 'success');
            },
            // 验证失败的回调函数
            validFail: (res, c, tac) => {
                console.log("注册验证码验证失败", res);
                registerCaptchaValidToken = null;
                showToast('验证码验证失败，请重试', 'error');
                // 验证失败后重新拉取验证码
                tac.reloadCaptcha();
            },
            // 刷新按钮回调事件
            btnRefreshFun: (el, tac) => {
                console.log("刷新注册验证码");
                registerCaptchaValidToken = null;
                tac.reloadCaptcha();
            },
            // 关闭按钮回调事件
            btnCloseFun: (el, tac) => {
                console.log("关闭注册验证码");
                tac.destroyWindow();
            }
        };

        // 样式配置
        let style = {
            logoUrl: null // 去除logo
        };

        // 初始化TAC验证码
        window.initTAC("/tac", config, style).then(tac => {
            registerTacInstance = tac;
            tac.init(); // 显示验证码
        }).catch(e => {
            console.log("初始化注册验证码失败", e);
            showToast("注册验证码初始化失败，请刷新页面重试", 'error');
        });
    }

    // 切换表单显示
    function toggleForms(showRegisterForm) {
        loginForm.style.display = showRegisterForm ? 'none' : 'block';
        registerForm.style.display = showRegisterForm ? 'block' : 'none';
        formTitle.textContent = showRegisterForm ? '创建账号' : '登录账号';
        document.querySelector('.subtitle').textContent = showRegisterForm ?
            '填写以下信息创建您的账号' : '欢迎回来，请输入您的账号信息';
    }

    showRegister.addEventListener('click', (e) => {
        e.preventDefault();
        toggleForms(true);
    });

    showLogin.addEventListener('click', (e) => {
        e.preventDefault();
        toggleForms(false);
    });

    // 登录表单提交
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // 检查验证码是否已验证
        if (!captchaValidToken) {
            showToast('请先完成验证码验证！', 'error');
            return;
        }

        const submitBtn = loginForm.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '登录中...';

        try {
            const response = await fetch('/api/user/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    captchaCode: captchaValidToken, // 使用验证码token
                    sessionId: captchaValidToken, // 使用验证码token作为sessionId
                    ip: await getClientIP()
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                localStorage.setItem('token', data.data.token);

                // 设置JWT_TOKEN到cookie中，有效期1天
                document.cookie = `JWT_TOKEN=${data.data.token}; path=/; max-age=86400; SameSite=Strict;`;

                showToast('登录成功', 'success');
                setTimeout(() => window.location.href = '/index.html', 1000);
            } else {
                showToast(data.message, 'error');
                // 登录失败时重置验证码
                captchaValidToken = null;
                if (tacInstance) {
                    tacInstance.reloadCaptcha();
                }
            }
        } catch (error) {
            console.error('登录失败:', error);
            showToast('登录失败，请重试', 'error');
            // 网络错误时也重置验证码
            captchaValidToken = null;
            if (tacInstance) {
                tacInstance.reloadCaptcha();
            }
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '登录';
        }
    });

    // 注册表单提交
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // 检查验证码是否已验证
        if (!registerCaptchaValidToken) {
            showToast('请先完成验证码验证！', 'error');
            return;
        }

        const submitBtn = registerForm.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '注册中...';

        try {
            const response = await fetch('/api/user/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: document.getElementById('reg-username').value,
                    password: document.getElementById('reg-password').value,
                    email: document.getElementById('reg-email').value,
                    captchaToken: registerCaptchaValidToken // 添加验证码token
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                showToast('注册成功', 'success');
                setTimeout(() => toggleForms(false), 1000);
            } else {
                showToast(data.message, 'error');
                // 注册失败时重置验证码
                registerCaptchaValidToken = null;
                if (registerTacInstance) {
                    registerTacInstance.reloadCaptcha();
                }
            }
        } catch (error) {
            console.error('注册失败:', error);
            showToast('注册失败，请重试', 'error');
            // 网络错误时也重置验证码
            registerCaptchaValidToken = null;
            if (registerTacInstance) {
                registerTacInstance.reloadCaptcha();
            }
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '注册';
        }
    });

    // 获取客户端IP
    async function getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('获取IP失败:', error);
            return '';
        }
    }

    // 提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }, 100);
    }
}); 