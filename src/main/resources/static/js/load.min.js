/**
 * TIANAI-CAPTCHA 验证码加载器
 * 简化版本，用于加载TAC验证码组件
 */
(function(window) {
    'use strict';

    // 检查是否已经加载
    if (window.initTAC) {
        return;
    }

    /**
     * 初始化TAC验证码
     * @param {string} tacPath TAC文件路径前缀
     * @param {object} config 验证码配置
     * @param {object} style 样式配置
     * @returns {Promise} 返回TAC实例的Promise
     */
    window.initTAC = function(tacPath, config, style) {
        return new Promise(function(resolve, reject) {
            // 检查必要参数
            if (!tacPath || !config) {
                reject(new Error('缺少必要参数'));
                return;
            }

            // 默认配置
            config = Object.assign({
                requestCaptchaDataUrl: '/api/tac/gen',
                validCaptchaUrl: '/api/tac/check',
                bindEl: '#captcha-box'
            }, config);

            style = style || {};

            // 加载CSS
            loadCSS(tacPath + '/css/tac.css').then(function() {
                // 加载JS
                return loadJS(tacPath + '/js/tac.min.js');
            }).then(function() {
                // 检查TAC是否已加载
                if (typeof window.TAC === 'undefined') {
                    throw new Error('TAC组件加载失败');
                }

                // 创建TAC实例
                var tacInstance = new window.TAC(config, style);
                resolve(tacInstance);
            }).catch(function(error) {
                reject(error);
            });
        });
    };

    /**
     * 加载CSS文件
     */
    function loadCSS(url) {
        return new Promise(function(resolve, reject) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = url;
            link.onload = function() {
                resolve();
            };
            link.onerror = function() {
                reject(new Error('CSS加载失败: ' + url));
            };
            document.head.appendChild(link);
        });
    }

    /**
     * 加载JS文件
     */
    function loadJS(url) {
        return new Promise(function(resolve, reject) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;
            script.onload = function() {
                resolve();
            };
            script.onerror = function() {
                reject(new Error('JS加载失败: ' + url));
            };
            document.head.appendChild(script);
        });
    }

})(window);
