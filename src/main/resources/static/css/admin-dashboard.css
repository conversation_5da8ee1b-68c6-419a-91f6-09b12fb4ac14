/* 管理员后台样式 */
:root {
    --primary-color: #0066FF;
    --primary-dark: #0052CC;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
    --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f7fa;
    color: var(--dark-color);
    overflow-x: hidden;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    /* 防止拖拽 */
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: var(--header-height);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.logo i {
    font-size: 2rem;
    background: var(--admin-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 菜单样式 */
.sidebar-menu {
    padding: 20px 0;
    flex: 1;
    overflow-y: auto;
}

.menu-section {
    margin-bottom: 30px;
}

.menu-title {
    padding: 0 20px 10px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.6);
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .menu-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.menu-list {
    list-style: none;
}

.menu-item {
    margin-bottom: 4px;
}

.menu-link {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 14px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0 25px 25px 0;
    margin-right: 20px;
}

.menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.menu-item.active .menu-link {
    background: var(--admin-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.menu-link i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.menu-link span {
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .menu-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .menu-link {
    justify-content: center;
    margin-right: 0;
    border-radius: 12px;
    margin: 4px 16px;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--admin-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-details {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .user-details {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

.logout-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

/* 主内容区域 */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #f5f7fa;
    position: relative;
    z-index: 1;
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 顶部导航栏 */
.top-header {
    background: white;
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    /* 防止拖拽 */
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    color: var(--dark-color);
}

.mobile-menu-toggle:hover {
    background-color: var(--light-color);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.1rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 10px;
    color: var(--secondary-color);
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* 用户菜单 */
.user-menu {
    position: relative;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 12px;
    transition: background-color 0.2s ease;
}

.user-menu-toggle:hover {
    background-color: var(--light-color);
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name-small {
    font-weight: 500;
    color: var(--dark-color);
}

.user-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
    min-width: 200px;
    padding: 8px 0;
    display: none;
    z-index: 1000;
}

.user-menu-dropdown.show {
    display: block;
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--dark-color);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 8px 0;
}

/* 仪表盘内容 */
.dashboard-content {
    padding: 30px;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--admin-gradient);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-icon.bg-primary { background: var(--primary-color); }
.stat-icon.bg-success { background: var(--success-color); }
.stat-icon.bg-warning { background: var(--warning-color); }
.stat-icon.bg-info { background: var(--info-color); }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    font-weight: 500;
    margin-bottom: 8px;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0;
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.chart-actions .form-select {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 0.85rem;
}

.chart-body {
    padding: 24px;
    height: 300px;
}

/* 底部网格 */
.bottom-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.quick-actions-card,
.recent-activities-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0;
    padding-bottom: 16px;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.view-all-link:hover {
    text-decoration: underline;
}

.card-body {
    padding: 24px;
}

/* 快速操作 */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.quick-action {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 12px;
    text-decoration: none;
    color: var(--dark-color);
    background: var(--light-color);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.quick-action:hover {
    background: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 102, 255, 0.1);
    color: var(--dark-color);
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.action-text {
    flex: 1;
}

.action-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 4px;
}

.action-desc {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

/* 最新动态 */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.activity-desc {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-bottom: 4px;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.loading-text {
    margin-top: 1rem;
    color: var(--primary-color);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .bottom-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .dashboard-content {
        padding: 20px 16px;
    }
    
    .top-header {
        padding: 0 16px;
    }
    
    .user-name-small {
        display: none;
    }
}

/* 防止头像或其他元素覆盖主要内容 */
.large-avatar {
    position: static !important;
    max-width: 120px !important;
    max-height: 120px !important;
    z-index: auto !important;
}

/* 确保所有头像都有合理的尺寸 */
img[class*="avatar"] {
    max-width: 120px;
    max-height: 120px;
    position: static;
}

/* 防止任何固定定位的元素覆盖主要内容 */
body > *:not(.sidebar):not(.main-content) {
    z-index: 999 !important;
}

/* 确保仪表盘内容区域在正确的层级 */
.dashboard-content {
    position: relative;
    z-index: 2;
    background: transparent;
}

/* 自定义滚动条 */
.sidebar-menu::-webkit-scrollbar,
.activity-list::-webkit-scrollbar {
    width: 6px;
}

.sidebar-menu::-webkit-scrollbar-track,
.activity-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.sidebar-menu::-webkit-scrollbar-thumb,
.activity-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover,
.activity-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
