2025-06-26 02:34:55.996 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:34:56.019 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:36:56.619 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:36:56.635 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:38:47.268 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:38:47.281 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:38:54.389 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:38:54.403 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:41:54.344 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:41:54.359 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:43:24.040 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:43:24.056 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:48:11.827 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 02:48:11.840 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 02:49:09.198 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-06-26 02:49:09.250 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=0, pages=0, current=1, size=5, records=0
2025-06-26 02:49:50.508 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-06-26 02:49:50.518 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=0, pages=0, current=1, size=5, records=0
2025-06-26 02:50:19.314 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60}
2025-06-26 02:50:19.385 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 60, 总可用题目: 737, 警告数量: 0
2025-06-26 02:50:21.244 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60}
2025-06-26 02:50:21.314 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 60, 总可用题目: 737, 警告数量: 0
2025-06-26 02:50:22.983 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20}
2025-06-26 02:50:23.051 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 80, 总可用题目: 826, 警告数量: 0
2025-06-26 02:50:23.992 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20}
2025-06-26 02:50:24.055 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 80, 总可用题目: 826, 警告数量: 0
2025-06-26 02:50:25.401 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:25.465 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 100, 总可用题目: 1155, 警告数量: 0
2025-06-26 02:50:26.797 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:26.856 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 100, 总可用题目: 1155, 警告数量: 0
2025-06-26 02:50:27.820 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:27.884 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 100, 总可用题目: 1155, 警告数量: 0
2025-06-26 02:50:29.339 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 1, 题型配置: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:29.402 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 100, 总可用题目: 1155, 警告数量: 0
2025-06-26 02:50:29.512 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='test3', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=245, knowledgeName=null, questionCount=100, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, DifficultyCriteria={easy=0.3, medium=0.5, hard=0.2}
2025-06-26 02:50:29.513 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=245, knowledgeName=null, questionCount=100, includeShortAnswer=false, shortAnswerCount=0)], title=test3, totalScore=300, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, minReuseIntervalDays=null)
2025-06-26 02:50:29.519 [http-nio-8081-exec-4] ERROR com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Request validation failed: [权重总和必须等于1.0，当前为0.820]
2025-06-26 02:50:29.530 [http-nio-8081-exec-4] ERROR com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Error during request validation: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
com.edu.maizi_edu_sys.exception.PaperGenerationException: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
	at com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine.generatePaper(PaperGenerationEngine.java:103) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl.generatePaper(PaperGenerationServiceImpl.java:164) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$FastClassBySpringCGLIB$$e109e2fc.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$EnhancerBySpringCGLIB$$7165d09.generatePaper(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperController.generatePaper(PaperController.java:130) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
2025-06-26 02:50:30.315 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-06-26 02:50:30.328 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=0, pages=0, current=1, size=5, records=0
2025-06-26 02:51:10.682 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-06-26 02:51:10.691 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=0, pages=0, current=1, size=5, records=0
2025-06-26 02:52:59.386 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='权重配置测试试卷', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=1, knowledgeName=null, questionCount=10, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=10, MULTIPLE_CHOICE=5, JUDGMENT=5}, GlobalTypeScores={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5}, DifficultyCriteria={easy=0.3, medium=0.5, hard=0.2}
2025-06-26 02:52:59.386 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=1, knowledgeName=null, questionCount=10, includeShortAnswer=false, shortAnswerCount=0)], title=权重配置测试试卷, totalScore=100, typeScoreMap={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=10, MULTIPLE_CHOICE=5, JUDGMENT=5}, minReuseIntervalDays=null)
2025-06-26 02:52:59.386 [http-nio-8081-exec-1] ERROR com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Request validation failed: [权重总和必须等于1.0，当前为0.820]
2025-06-26 02:52:59.388 [http-nio-8081-exec-1] ERROR com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Error during request validation: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
com.edu.maizi_edu_sys.exception.PaperGenerationException: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
	at com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine.generatePaper(PaperGenerationEngine.java:103) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl.generatePaper(PaperGenerationServiceImpl.java:164) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$FastClassBySpringCGLIB$$e109e2fc.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$EnhancerBySpringCGLIB$$7165d09.generatePaper(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperController.generatePaper(PaperController.java:130) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
2025-06-26 03:05:01.851 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 03:05:01.866 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 03:20:48.922 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 03:20:48.939 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 03:23:45.242 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 03:23:45.258 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 03:34:31.060 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 03:34:31.073 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 08:59:48.342 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 08:59:48.357 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
2025-06-26 09:58:34.910 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - TagDiversityAnalyzer successfully injected and ready for use
2025-06-26 09:58:34.930 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator and Validator.
