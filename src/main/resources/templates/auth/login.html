<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - <PERSON>zi EDU</title>
    <link rel="stylesheet" type="text/css" href="/static/css/login.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <!-- 引入验证码初始化js -->
    <script src="/static/js/load.min.js"></script>
</head>
<body>
    <div class="page-container">
        <div class="left-section">
            <div class="brand">
                <h1>Maizi EDU</h1>
                <p class="slogan">智慧教育，未来可期</p>
            </div>
        </div>
        
        <div class="right-section">
            <div class="form-container">
                <div class="form-header">
                    <h2 id="formTitle">登录账号</h2>
                    <p class="subtitle">欢迎回来，请输入您的账号信息</p>
                </div>

                <form id="loginForm" class="active">
                    <div class="input-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" required>
                    </div>
                    <div class="input-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" required>
                        <span class="forgot-password">忘记密码？</span>
                    </div>
                    <div class="input-group">
                        <label for="captcha">验证码</label>
                        <!-- TIANAI-CAPTCHA 滑块验证码容器 -->
                        <div id="user-captcha-box" class="captcha-container"></div>
                        <small class="captcha-tip">拖动滑块完成验证</small>
                    </div>
                    <button type="submit" class="submit-btn">登录</button>
                    <p class="switch-form">
                        还没有账号？<a href="#" id="showRegister">立即注册</a>
                    </p>
                    <div class="admin-login-tip">
                        <p class="admin-tip-text">
                            <i class="admin-icon">🔐</i>
                            管理员请使用 <a href="/admin/login" class="admin-link">管理员登录入口</a>
                        </p>
                    </div>
                </form>

                <form id="registerForm" style="display: none;">
                    <div class="input-group">
                        <label for="reg-username">用户名</label>
                        <input type="text" id="reg-username" required>
                    </div>
                    <div class="input-group">
                        <label for="reg-password">密码</label>
                        <input type="password" id="reg-password" required>
                    </div>
                    <div class="input-group">
                        <label for="reg-email">邮箱</label>
                        <input type="email" id="reg-email" required>
                    </div>
                    <div class="input-group">
                        <label for="reg-captcha">验证码</label>
                        <!-- TIANAI-CAPTCHA 滑块验证码容器 -->
                        <div id="register-captcha-box" class="captcha-container"></div>
                        <small class="captcha-tip">拖动滑块完成验证</small>
                    </div>
                    <button type="submit" class="submit-btn">注册</button>
                    <p class="switch-form">
                        已有账号？<a href="#" id="showLogin">返回登录</a>
                    </p>
                </form>
            </div>
        </div>
    </div>
    <script src="/static/js/login.js"></script>
</body>
</html> 