<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #666;
            margin-bottom: 15px;
        }
        .captcha-container {
            margin: 20px 0;
            min-height: 200px;
            border: 1px dashed #ccc;
            border-radius: 5px;
            padding: 10px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
    <!-- 引入验证码初始化js -->
    <script src="/static/js/load.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>TIANAI-CAPTCHA 验证码测试</h1>
        
        <!-- 滑块验证码测试 -->
        <div class="test-section">
            <h2>滑块验证码 (SLIDER)</h2>
            <div id="slider-captcha" class="captcha-container"></div>
            <button class="btn" onclick="initSliderCaptcha()">初始化滑块验证码</button>
            <button class="btn" onclick="reloadSliderCaptcha()">刷新验证码</button>
            <div id="slider-result" class="result"></div>
        </div>
        
        <!-- 旋转验证码测试 -->
        <div class="test-section">
            <h2>旋转验证码 (ROTATE)</h2>
            <div id="rotate-captcha" class="captcha-container"></div>
            <button class="btn" onclick="initRotateCaptcha()">初始化旋转验证码</button>
            <button class="btn" onclick="reloadRotateCaptcha()">刷新验证码</button>
            <div id="rotate-result" class="result"></div>
        </div>
        
        <!-- 点选验证码测试 -->
        <div class="test-section">
            <h2>点选验证码 (WORD_IMAGE_CLICK)</h2>
            <div id="click-captcha" class="captcha-container"></div>
            <button class="btn" onclick="initClickCaptcha()">初始化点选验证码</button>
            <button class="btn" onclick="reloadClickCaptcha()">刷新验证码</button>
            <div id="click-result" class="result"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let sliderTac = null;
        let rotateTac = null;
        let clickTac = null;

        // 初始化滑块验证码
        function initSliderCaptcha() {
            const config = {
                requestCaptchaDataUrl: "/api/tac/gen?type=SLIDER",
                validCaptchaUrl: "/api/tac/check",
                bindEl: "#slider-captcha",
                validSuccess: (res, c, tac) => {
                    showResult('slider-result', '滑块验证码验证成功！', 'success');
                    console.log('滑块验证成功:', res);
                },
                validFail: (res, c, tac) => {
                    showResult('slider-result', '滑块验证码验证失败！', 'error');
                    console.log('滑块验证失败:', res);
                    tac.reloadCaptcha();
                },
                btnRefreshFun: (el, tac) => {
                    console.log('刷新滑块验证码');
                    tac.reloadCaptcha();
                }
            };

            window.initTAC("/tac", config, {logoUrl: null}).then(tac => {
                sliderTac = tac;
                tac.init();
                showResult('slider-result', '滑块验证码初始化成功', 'success');
            }).catch(e => {
                showResult('slider-result', '滑块验证码初始化失败: ' + e.message, 'error');
            });
        }

        // 初始化旋转验证码
        function initRotateCaptcha() {
            const config = {
                requestCaptchaDataUrl: "/api/tac/gen?type=ROTATE",
                validCaptchaUrl: "/api/tac/check",
                bindEl: "#rotate-captcha",
                validSuccess: (res, c, tac) => {
                    showResult('rotate-result', '旋转验证码验证成功！', 'success');
                    console.log('旋转验证成功:', res);
                },
                validFail: (res, c, tac) => {
                    showResult('rotate-result', '旋转验证码验证失败！', 'error');
                    console.log('旋转验证失败:', res);
                    tac.reloadCaptcha();
                },
                btnRefreshFun: (el, tac) => {
                    console.log('刷新旋转验证码');
                    tac.reloadCaptcha();
                }
            };

            window.initTAC("/tac", config, {logoUrl: null}).then(tac => {
                rotateTac = tac;
                tac.init();
                showResult('rotate-result', '旋转验证码初始化成功', 'success');
            }).catch(e => {
                showResult('rotate-result', '旋转验证码初始化失败: ' + e.message, 'error');
            });
        }

        // 初始化点选验证码
        function initClickCaptcha() {
            const config = {
                requestCaptchaDataUrl: "/api/tac/gen?type=WORD_IMAGE_CLICK",
                validCaptchaUrl: "/api/tac/check",
                bindEl: "#click-captcha",
                validSuccess: (res, c, tac) => {
                    showResult('click-result', '点选验证码验证成功！', 'success');
                    console.log('点选验证成功:', res);
                },
                validFail: (res, c, tac) => {
                    showResult('click-result', '点选验证码验证失败！', 'error');
                    console.log('点选验证失败:', res);
                    tac.reloadCaptcha();
                },
                btnRefreshFun: (el, tac) => {
                    console.log('刷新点选验证码');
                    tac.reloadCaptcha();
                }
            };

            window.initTAC("/tac", config, {logoUrl: null}).then(tac => {
                clickTac = tac;
                tac.init();
                showResult('click-result', '点选验证码初始化成功', 'success');
            }).catch(e => {
                showResult('click-result', '点选验证码初始化失败: ' + e.message, 'error');
            });
        }

        // 刷新验证码函数
        function reloadSliderCaptcha() {
            if (sliderTac) {
                sliderTac.reloadCaptcha();
            } else {
                showResult('slider-result', '请先初始化滑块验证码', 'error');
            }
        }

        function reloadRotateCaptcha() {
            if (rotateTac) {
                rotateTac.reloadCaptcha();
            } else {
                showResult('rotate-result', '请先初始化旋转验证码', 'error');
            }
        }

        function reloadClickCaptcha() {
            if (clickTac) {
                clickTac.reloadCaptcha();
            } else {
                showResult('click-result', '请先初始化点选验证码', 'error');
            }
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
            element.style.display = 'block';
            
            // 3秒后隐藏
            setTimeout(() => {
                element.style.display = 'none';
            }, 3000);
        }

        // 页面加载完成后自动初始化滑块验证码
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，准备测试验证码功能');
        });
    </script>
</body>
</html>
