<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<title>管理员登录 - 麦子教育系统</title>
	<link rel="stylesheet" href="../assets/libs/particles/css/style.css">
	<link rel="stylesheet" href="../assets/css/base.css">
	<link rel="stylesheet" href="../assets/css/login.css" />
	<!-- 引入验证码初始化js -->
	<script src="/static/js/load.min.js"></script>
</head>
<body>
	<!-- particles.js container -->
	<div id="particles-js"></div>
	<div id="wrapper">
		<div>
			<img src="../assets/img/zhihu_logo.png" />
			<h2>麦子教育系统 - 管理员后台</h2>
		</div>
		<nav class="switch_nav">
			<a href="register.html" id="switch_signup" class="switch_btn">注册</a>
			<a href="javascript:;" id="switch_login" class="switch_btn on">登录</a>
			<div class="switch_bottom" id="switch_bottom"></div>
		</nav>
		<div id="login">
			<form method="post" action="" id="loginForm">
				<ul class="group_input">
					<li>
						<input type="text" class="mobile required" id="username" placeholder="管理员用户名" />
					</li>
					<li>
						<input type="password" class="psd required" id="password" placeholder="密码" />
					</li>
					<li>
						<!-- 验证码容器 -->
						<div id="captcha-box" style="margin: 10px 0;"></div>
					</li>
				</ul>
				<button type="button" class="submit_btn" id="btnSubmit">登录</button>
			</form>
			<div class="states">
				<span class="left"><a href="javascript:;">手机验证码登陆</a></span>
				<span class="right"><a href="javascript:;">无法登陆？</a></span>
			</div>
			<div class="states">
				<a href="javascript:;" class="social_account">社交账号登陆</a>
				<div class="states three_MinIcon">
					<a href="javascript:;" class="MinIcon_weixin"><img src="../assets/img/icon_weixin.jpg" style="width:20px;height:18px" /></a>
					<a href="javascript:;" class="MinIcon_weibo"><img src="../assets/img/icon_weibo.jpg" style="width:20px;height:18px" /></a>
					<a href="javascript:;" class="MinIcon_qq"><img src="../assets/img/icon_qq.jpg" style="width:20px;height:18px" /></a>
				</div>
			</div>
		</div>
		<div class="QRcode_btn">
			<div type="submit" class="submit_btn download_btn">下载知乎App</div>
			<div class="QRcode">
				<img src="../assets/img/QRcode.png" alt="QRcode" />
				<div class="box"></div>
			</div>

		</div>
		<div id="footer">
			<span>&copy;2017知乎</span><span>·</span><a href="javascript:;">知乎圆桌</a><span>·</span><a href="javascript:;">发现</a><span>·</span><a href="javascript:;">移动应用</a><span>·</span><a href="javascript:;">使用机构账号登录</a><span>·</span><a href="javascript:;">联系我们</a><span>·</span><a href="javascript:;">工作来知乎</a><br />
			<span>·</span><a href="javascript:;">京ICP证110745号</a><span>·</span><span>京公网安备11010802010035号</span><span>·</span><a href="javascript:;">出版物经营许可证</a>
		</div>
	</div>
	<script src="../assets/libs/jquery-1.12.4/jquery.min.js"></script>
	<script src="../assets/libs/particles/particles.min.js"></script>
	<script src="../assets/libs/particles/js/app.js"></script>
	<!-- <script src="../assets/libs/particles/js/lib/stats.js"></script> -->
	<script>
		var count_particles, stats, update;
		stats = new Stats;
		stats.setMode(0);
		stats.domElement.style.position = 'absolute';
		stats.domElement.style.left = '0px';
		stats.domElement.style.top = '0px';
		document.body.appendChild(stats.domElement);
		count_particles = document.querySelector('.js-count-particles');
		update = function() {
			stats.begin();
			stats.end();
			if (window.pJSDom[0].pJS.particles && window.pJSDom[0].pJS.particles.array) {
				count_particles.innerText = window.pJSDom[0].pJS.particles.array.length;
			}
			requestAnimationFrame(update);
		};
		requestAnimationFrame(update);
	</script>
	<script>
		$(".download_btn").click(function(){
			if($(".QRcode").css("display")=="none"){
				$(".QRcode").show();
				$(".download_btn").text("关闭二维码");
			}else{
				$(".QRcode").hide();
				$(".download_btn").text("下载知乎App");
			}
		});	
	</script>
	<script>
		// 全局变量
		let tacInstance = null;
		let captchaValidToken = null;

		$(function(){
			// 初始化验证码
			initCaptcha();

			//为表单元素添加失去焦点事件
			$("form :input").blur(function(){
				var $parent = $(this).parent();
				$parent.find(".msg").remove(); //删除以前的提醒元素

				//验证用户名
				if($(this).is("#username")){
					var usernameVal = $.trim(this.value);
					if(usernameVal == ""){
						var errorMsg = " 请输入管理员用户名！";
						$parent.append("<span class='msg onError'>" + errorMsg + "</span>");
					} else{
						var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
					}
				}
				//验证密码
	            if($(this).is("#password")){
	                var psdVal = $.trim(this.value);
	                if(psdVal == "" || psdVal.length < 6){
	                    var errorMsg = " 密码不能少于6位！";
	                    $parent.append("<span class='msg onError'>" + errorMsg + "</span>");
	                }
	                else{
	                    var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
	                }
	            }
			}).keyup(function(){
				//triggerHandler 防止事件执行完后，浏览器自动为标签获得焦点
				$(this).triggerHandler("blur");
			}).focus(function(){
				$(this).triggerHandler("blur");
			});

			// 登录按钮点击事件
			$("#btnSubmit").click(function(){
				// 验证表单
				$("form .required:input").trigger("blur");
				var numError = $("form .onError").length;
				if(numError){
					return false;
				}

				// 检查验证码是否已验证
				if(!captchaValidToken){
					alert('请先完成验证码验证！');
					return false;
				}

				// 执行登录
				performLogin();
			});
		});

		// 初始化验证码
		function initCaptcha() {
			const config = {
				// 生成接口
				requestCaptchaDataUrl: "/api/tac/gen",
				// 验证接口
				validCaptchaUrl: "/api/tac/check",
				// 验证码绑定的div块
				bindEl: "#captcha-box",
				// 验证成功回调函数
				validSuccess: (res, c, tac) => {
					console.log("验证码验证成功", res);
					captchaValidToken = res.data.validToken;
					// 不销毁验证码，保持显示状态
					// tac.destroyWindow();
				},
				// 验证失败的回调函数
				validFail: (res, c, tac) => {
					console.log("验证码验证失败", res);
					captchaValidToken = null;
					// 验证失败后重新拉取验证码
					tac.reloadCaptcha();
				},
				// 刷新按钮回调事件
				btnRefreshFun: (el, tac) => {
					console.log("刷新验证码");
					captchaValidToken = null;
					tac.reloadCaptcha();
				},
				// 关闭按钮回调事件
				btnCloseFun: (el, tac) => {
					console.log("关闭验证码");
					tac.destroyWindow();
				}
			};

			// 样式配置
			let style = {
				logoUrl: null // 去除logo
			};

			// 初始化TAC验证码
			window.initTAC("/tac", config, style).then(tac => {
				tacInstance = tac;
				tac.init(); // 显示验证码
			}).catch(e => {
				console.log("初始化验证码失败", e);
				alert("验证码初始化失败，请刷新页面重试");
			});
		}

		// 执行登录
		function performLogin() {
			const username = $("#username").val().trim();
			const password = $("#password").val().trim();

			if(!username || !password) {
				alert('请填写完整的登录信息！');
				return;
			}

			// 发送登录请求
			$.ajax({
				url: '/api/admin/login',
				type: 'POST',
				contentType: 'application/json',
				data: JSON.stringify({
					username: username,
					password: password,
					captchaToken: captchaValidToken
				}),
				success: function(response) {
					if(response.success) {
						alert('登录成功！');
						// 跳转到管理员后台主页
						window.location.href = '/admin/dashboard-new';
					} else {
						alert('登录失败：' + response.message);
						// 重置验证码
						captchaValidToken = null;
						if(tacInstance) {
							tacInstance.reloadCaptcha();
						}
					}
				},
				error: function(xhr, status, error) {
					console.error('登录请求失败:', error);
					alert('登录请求失败，请稍后重试');
					// 重置验证码
					captchaValidToken = null;
					if(tacInstance) {
						tacInstance.reloadCaptcha();
					}
				}
			});
		}
	</script>
</body>
</html>
