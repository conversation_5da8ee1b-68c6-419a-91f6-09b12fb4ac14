<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<title>管理员注册 - 麦子教育系统</title>
	<link rel="stylesheet" href="../assets/libs/particles/css/style.css">
	<link rel="stylesheet" href="../assets/libs/sweetalert2/sweetalert2.min.css">
	<link rel="stylesheet" href="../assets/css/base.css">
	<link rel="stylesheet" href="../assets/css/login.css">
	<!-- 引入验证码初始化js -->
	<script src="/static/js/load.min.js"></script>
</head>
<body>
	<!-- particles.js container -->
	<div id="particles-js"></div>
	<div id="wrapper">
		<div>
			<img src="../assets/img/zhihu_logo.png" />
			<h2>麦子教育系统 - 管理员注册</h2>
		</div>
		<nav class="switch_nav">
			<a href="javascript:;" id="switch_signup" class="switch_btn on">注册</a>
			<a href="login.html" id="switch_login" class="switch_btn">登录</a>
			<div class="switch_bottom" id="switch_bottom"></div>
		</nav>
		<form method="post" action="" id="registerForm">
			<ul class="group_input">
				<li>
					<input type="text" placeholder="管理员姓名" class="name required" id="name" />
				</li>
				<li>
					<input type="text" placeholder="用户名" class="username required" id="username" />
				</li>
				<li>
					<input type="email" placeholder="邮箱地址" class="email required" id="email" />
				</li>
				<li>
					<input type="password" placeholder="密码(不少于6位)" class="psd required" id="password" />
				</li>
				<li>
					<input type="password" placeholder="确认密码" class="confirm-psd required" id="confirmPassword" />
				</li>
				<li>
					<!-- 验证码容器 -->
					<div id="captcha-box" style="margin: 10px 0;"></div>
				</li>
			</ul>
			<button type="button" class="submit_btn" id="btnSubmit">注册管理员</button>
			<span class="agreement-tip">点击「注册」按钮，即代表你同意<a href="javascript:;">《麦子教育系统协议》</a></span>
		</form>
		<div class="QRcode_btn">
			<div type="submit" class="submit_btn download_btn">下载知乎App</div>
			<div class="QRcode">
				<img src="../assets/img/QRcode.png" alt="QRcode" />
				<div class="box"></div>
			</div>

		</div>
  
	
		<div id="footer">
			<span>&copy;2017知乎</span><span>·</span><a href="javascript:;">知乎圆桌</a><span>·</span><a href="javascript:;">发现</a><span>·</span><a href="javascript:;">移动应用</a><span>·</span><a href="javascript:;">使用机构账号登录</a><span>·</span><a href="javascript:;">联系我们</a><span>·</span><a href="javascript:;">工作来知乎</a><br />
			<span>·</span><a href="javascript:;">京ICP证110745号</a><span>·</span><span>京公网安备11010802010035号</span><span>·</span><a href="javascript:;">出版物经营许可证</a>
		</div>
	</div>
	<script src="../assets/libs/jquery-1.12.4/jquery.min.js"></script>
	<script src="../assets/libs/sweetalert2/sweetalert2.min.js"></script>
	<script src="../assets/libs/particles/particles.min.js"></script>
	<script src="../assets/libs/particles/js/app.js"></script>
	<!-- <script src="../assets/libs/particles/js/lib/stats.js"></script> -->
	<script>
		var count_particles, stats, update;
		stats = new Stats;
		stats.setMode(0);
		stats.domElement.style.position = 'absolute';
		stats.domElement.style.left = '0px';
		stats.domElement.style.top = '0px';
		document.body.appendChild(stats.domElement);
		count_particles = document.querySelector('.js-count-particles');
		update = function() {
			stats.begin();
			stats.end();
			if (window.pJSDom[0].pJS.particles && window.pJSDom[0].pJS.particles.array) {
				count_particles.innerText = window.pJSDom[0].pJS.particles.array.length;
			}
			requestAnimationFrame(update);
		};
		requestAnimationFrame(update);
	</script>	
	<script>
		$(".download_btn").click(function(){
			if($(".QRcode").css("display")=="none"){
				$(".QRcode").show();
				$(".download_btn").text("关闭二维码");
			}else{
				$(".QRcode").hide();
				$(".download_btn").text("下载知乎App");
			}
		});	
	</script>
	<script>
		// 全局变量
		let tacInstance = null;
		let captchaValidToken = null;

		$(function(){
			// 初始化验证码
			initCaptcha();

			//为表单元素添加失去焦点事件
			$("form :input").blur(function(){
				var $parent = $(this).parent();
				$parent.find(".msg").remove(); //删除以前的提醒元素

				//验证姓名
				if($(this).is("#name")){
					var nameVal = $.trim(this.value);
					var regName = /[~#^$@%&!*()<>:;'"{}【】  ]/;
					if(nameVal == "" || nameVal.length < 2 || regName.test(nameVal)){
						var errorMsg = " 姓名非空，长度2-20位，不包含特殊字符！";
						$parent.append("<span class='msg onError'>" + errorMsg + "</span>");
					} else{
						var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
					}
				}

				//验证用户名
				if($(this).is("#username")){
					var usernameVal = $.trim(this.value);
					var regUsername = /^[a-zA-Z0-9_]{3,20}$/;
					if(usernameVal == "" || !regUsername.test(usernameVal)){
						var errorMsg = " 用户名为3-20位字母、数字或下划线！";
						$parent.append("<span class='msg onError'>" + errorMsg + "</span>");
					} else{
						var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
					}
				}

				//验证邮箱
				if($(this).is("#email")){
					var emailVal = $.trim(this.value);
					var regEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if(emailVal == "" || !regEmail.test(emailVal)){
						var errorMsg = " 请输入有效的邮箱地址！";
						$parent.append("<span class='msg onError'>" + errorMsg + "</span>");
					} else{
						var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
					}
				}

				//验证密码
	            if($(this).is("#password")){
	                var psdVal = $.trim(this.value);
	                if(psdVal == "" || psdVal.length < 6){
	                    var errorMsg = " 密码不能少于6位！";
	                    $parent.append("<span class='msg onError'>" + errorMsg + "</span>");
	                }
	                else{
	                    var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
	                }
	            }

	            //验证确认密码
	            if($(this).is("#confirmPassword")){
	                var confirmPsdVal = $.trim(this.value);
	                var psdVal = $.trim($("#password").val());
	                if(confirmPsdVal == "" || confirmPsdVal !== psdVal){
	                    var errorMsg = " 两次密码输入不一致！";
	                    $parent.append("<span class='msg onError'>" + errorMsg + "</span>");
	                }
	                else{
	                    var okMsg=" 输入正确";
	                    $parent.append("<span class='msg onSuccess'>" + okMsg + "</span>");
	                }
	            }
			}).keyup(function(){
				//triggerHandler 防止事件执行完后，浏览器自动为标签获得焦点
				$(this).triggerHandler("blur");
			}).focus(function(){
				$(this).triggerHandler("blur");
			});

			// 注册按钮点击事件
			$("#btnSubmit").click(function(){
				// 验证表单
				$("form .required:input").trigger("blur");
				var numError = $("form .onError").length;
				if(numError){
					return false;
				}

				// 检查验证码是否已验证
				if(!captchaValidToken){
					alert('请先完成验证码验证！');
					return false;
				}

				// 执行注册
				performRegister();
			});
		});

		// 初始化验证码
		function initCaptcha() {
			const config = {
				// 生成接口
				requestCaptchaDataUrl: "/api/tac/gen",
				// 验证接口
				validCaptchaUrl: "/api/tac/check",
				// 验证码绑定的div块
				bindEl: "#captcha-box",
				// 验证成功回调函数
				validSuccess: (res, c, tac) => {
					console.log("验证码验证成功", res);
					captchaValidToken = res.data.validToken;
				},
				// 验证失败的回调函数
				validFail: (res, c, tac) => {
					console.log("验证码验证失败", res);
					captchaValidToken = null;
					// 验证失败后重新拉取验证码
					tac.reloadCaptcha();
				},
				// 刷新按钮回调事件
				btnRefreshFun: (el, tac) => {
					console.log("刷新验证码");
					captchaValidToken = null;
					tac.reloadCaptcha();
				},
				// 关闭按钮回调事件
				btnCloseFun: (el, tac) => {
					console.log("关闭验证码");
					tac.destroyWindow();
				}
			};

			// 样式配置
			let style = {
				logoUrl: null // 去除logo
			};

			// 初始化TAC验证码
			window.initTAC("/tac", config, style).then(tac => {
				tacInstance = tac;
				tac.init(); // 显示验证码
			}).catch(e => {
				console.log("初始化验证码失败", e);
				alert("验证码初始化失败，请刷新页面重试");
			});
		}

		// 执行注册
		function performRegister() {
			const formData = {
				name: $("#name").val().trim(),
				username: $("#username").val().trim(),
				email: $("#email").val().trim(),
				password: $("#password").val().trim(),
				confirmPassword: $("#confirmPassword").val().trim(),
				captchaToken: captchaValidToken
			};

			if(!formData.name || !formData.username || !formData.email || !formData.password) {
				alert('请填写完整的注册信息！');
				return;
			}

			// 发送注册请求
			$.ajax({
				url: '/api/admin/register',
				type: 'POST',
				contentType: 'application/json',
				data: JSON.stringify(formData),
				success: function(response) {
					if(response.success) {
						alert('注册成功！请联系系统管理员激活账号。');
						// 跳转到登录页面
						window.location.href = '/admin/login';
					} else {
						alert('注册失败：' + response.message);
						// 重置验证码
						captchaValidToken = null;
						if(tacInstance) {
							tacInstance.reloadCaptcha();
						}
					}
				},
				error: function(xhr, status, error) {
					console.error('注册请求失败:', error);
					alert('注册请求失败，请稍后重试');
					// 重置验证码
					captchaValidToken = null;
					if(tacInstance) {
						tacInstance.reloadCaptcha();
					}
				}
			});
		}
	</script>
</body>
</html>
