<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI修复测试页面 - <PERSON>zi EDU</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>UI修复测试页面</h1>
        <p class="text-muted">此页面用于测试前端界面修复的效果</p>

        <!-- 验证码测试 -->
        <div class="test-section">
            <h3><i class="bi bi-shield-check"></i> 验证码功能测试</h3>
            <p>测试验证码API响应格式修复</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>验证码图片</h5>
                    <img id="testCaptchaImage" src="" alt="验证码" style="border: 1px solid #ddd; border-radius: 4px;">
                    <br><br>
                    <button class="btn btn-primary" onclick="testLoadCaptcha()">加载验证码</button>
                    <button class="btn btn-secondary" onclick="testRefreshCaptcha()">刷新验证码</button>
                </div>
                <div class="col-md-6">
                    <h5>测试结果</h5>
                    <div id="captchaTestResult" class="test-result test-info">
                        点击"加载验证码"开始测试
                    </div>
                </div>
            </div>
        </div>

        <!-- jQuery测试 -->
        <div class="test-section">
            <h3><i class="bi bi-code-square"></i> jQuery加载测试</h3>
            <p>测试jQuery库是否正确加载</p>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary" onclick="testJQuery()">测试jQuery</button>
                    <button class="btn btn-secondary" onclick="testCommonJS()">测试common.js</button>
                </div>
                <div class="col-md-6">
                    <div id="jqueryTestResult" class="test-result test-info">
                        点击"测试jQuery"开始测试
                    </div>
                </div>
            </div>
        </div>

        <!-- 布局测试 -->
        <div class="test-section">
            <h3><i class="bi bi-layout-sidebar"></i> 布局测试</h3>
            <p>测试管理员界面布局修复</p>
            
            <div class="row">
                <div class="col-md-6">
                    <a href="/admin/dashboard" class="btn btn-primary" target="_blank">打开管理员界面</a>
                    <button class="btn btn-secondary" onclick="testLayoutCSS()">测试CSS规则</button>
                </div>
                <div class="col-md-6">
                    <div id="layoutTestResult" class="test-result test-info">
                        点击"测试CSS规则"开始测试
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>

    <script>
        let currentSessionId = null;

        // 测试验证码加载
        function testLoadCaptcha() {
            const resultDiv = document.getElementById('captchaTestResult');
            resultDiv.className = 'test-result test-info';
            resultDiv.textContent = '正在加载验证码...';

            fetch('/api/captcha/generate')
                .then(response => response.json())
                .then(data => {
                    console.log('验证码API响应:', data);
                    
                    if (data.code === 200 && data.data) {
                        document.getElementById('testCaptchaImage').src = data.data.captchaImage;
                        currentSessionId = data.data.sessionId;
                        
                        resultDiv.className = 'test-result test-success';
                        resultDiv.innerHTML = `
                            <strong>✓ 验证码加载成功</strong><br>
                            会话ID: ${currentSessionId}<br>
                            响应格式: 正确 (code: ${data.code})
                        `;
                    } else {
                        resultDiv.className = 'test-result test-error';
                        resultDiv.innerHTML = `
                            <strong>✗ 验证码加载失败</strong><br>
                            错误信息: ${data.message || '未知错误'}<br>
                            响应码: ${data.code}
                        `;
                    }
                })
                .catch(error => {
                    console.error('验证码加载失败:', error);
                    resultDiv.className = 'test-result test-error';
                    resultDiv.innerHTML = `
                        <strong>✗ 网络请求失败</strong><br>
                        错误信息: ${error.message}
                    `;
                });
        }

        // 测试验证码刷新
        function testRefreshCaptcha() {
            if (!currentSessionId) {
                alert('请先加载验证码');
                return;
            }

            const resultDiv = document.getElementById('captchaTestResult');
            resultDiv.className = 'test-result test-info';
            resultDiv.textContent = '正在刷新验证码...';

            fetch('/api/captcha/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ sessionId: currentSessionId })
            })
            .then(response => response.json())
            .then(data => {
                console.log('验证码刷新响应:', data);
                
                if (data.code === 200 && data.data) {
                    document.getElementById('testCaptchaImage').src = data.data.captchaImage;
                    currentSessionId = data.data.sessionId;
                    
                    resultDiv.className = 'test-result test-success';
                    resultDiv.innerHTML = `
                        <strong>✓ 验证码刷新成功</strong><br>
                        新会话ID: ${currentSessionId}<br>
                        响应格式: 正确 (code: ${data.code})
                    `;
                } else {
                    resultDiv.className = 'test-result test-error';
                    resultDiv.innerHTML = `
                        <strong>✗ 验证码刷新失败</strong><br>
                        错误信息: ${data.message || '未知错误'}<br>
                        响应码: ${data.code}
                    `;
                }
            })
            .catch(error => {
                console.error('验证码刷新失败:', error);
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `
                    <strong>✗ 网络请求失败</strong><br>
                    错误信息: ${error.message}
                `;
            });
        }

        // 测试jQuery
        function testJQuery() {
            const resultDiv = document.getElementById('jqueryTestResult');
            
            if (typeof $ !== 'undefined') {
                resultDiv.className = 'test-result test-success';
                resultDiv.innerHTML = `
                    <strong>✓ jQuery加载成功</strong><br>
                    版本: ${$.fn.jquery || '未知'}<br>
                    类型: ${typeof $}
                `;
            } else {
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `
                    <strong>✗ jQuery未加载</strong><br>
                    $ 变量类型: ${typeof $}
                `;
            }
        }

        // 测试common.js
        function testCommonJS() {
            const resultDiv = document.getElementById('jqueryTestResult');
            const functions = ['showToast', 'getAuthHeader', 'normalizeToken'];
            const results = [];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push(`✓ ${funcName}: 可用`);
                } else {
                    results.push(`✗ ${funcName}: 不可用`);
                }
            });
            
            const allAvailable = results.every(r => r.startsWith('✓'));
            
            resultDiv.className = allAvailable ? 'test-result test-success' : 'test-result test-error';
            resultDiv.innerHTML = `
                <strong>${allAvailable ? '✓' : '✗'} common.js函数测试</strong><br>
                ${results.join('<br>')}
            `;
        }

        // 测试布局CSS
        function testLayoutCSS() {
            const resultDiv = document.getElementById('layoutTestResult');
            const tests = [];
            
            // 检查CSS规则是否存在
            const stylesheets = Array.from(document.styleSheets);
            let hasAdminCSS = false;
            
            try {
                stylesheets.forEach(sheet => {
                    if (sheet.href && sheet.href.includes('admin-dashboard.css')) {
                        hasAdminCSS = true;
                    }
                });
                
                tests.push(hasAdminCSS ? '✓ admin-dashboard.css: 已加载' : '✗ admin-dashboard.css: 未加载');
                
                // 检查关键CSS变量
                const testElement = document.createElement('div');
                testElement.style.cssText = 'position: fixed; top: -9999px; left: -9999px;';
                testElement.className = 'sidebar';
                document.body.appendChild(testElement);
                
                const computedStyle = window.getComputedStyle(testElement);
                const position = computedStyle.position;
                const userSelect = computedStyle.userSelect || computedStyle.webkitUserSelect;
                
                tests.push(position === 'fixed' ? '✓ 侧边栏定位: fixed' : `✗ 侧边栏定位: ${position}`);
                tests.push(userSelect === 'none' ? '✓ 防拖拽: 已启用' : `✗ 防拖拽: ${userSelect}`);
                
                document.body.removeChild(testElement);
                
            } catch (error) {
                tests.push(`✗ CSS测试错误: ${error.message}`);
            }
            
            const allPassed = tests.every(t => t.startsWith('✓'));
            
            resultDiv.className = allPassed ? 'test-result test-success' : 'test-result test-error';
            resultDiv.innerHTML = `
                <strong>${allPassed ? '✓' : '✗'} CSS规则测试</strong><br>
                ${tests.join('<br>')}
            `;
        }

        // 页面加载完成后自动运行一些测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('UI修复测试页面已加载');
            
            // 自动测试jQuery
            setTimeout(testJQuery, 500);
            
            // 自动测试common.js
            setTimeout(testCommonJS, 1000);
        });
    </script>
</body>
</html>
