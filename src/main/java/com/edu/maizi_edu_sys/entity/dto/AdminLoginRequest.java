package com.edu.maizi_edu_sys.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 管理员登录请求DTO
 */
@Data
public class AdminLoginRequest {
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    /**
     * TIANAI-CAPTCHA验证码token
     */
    private String captchaToken;
    
    /**
     * 客户端IP地址
     */
    private String ip;
}
