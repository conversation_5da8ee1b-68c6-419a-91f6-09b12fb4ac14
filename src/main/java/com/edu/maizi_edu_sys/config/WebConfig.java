package com.edu.maizi_edu_sys.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.lang.NonNull;
import org.springframework.beans.factory.annotation.Autowired;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private AuthInterceptor authInterceptor;

    @Autowired
    private AdminInterceptor adminInterceptor;

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
                    "/",
                    "/main/chat",
                    "/topics/upload-topics",
                    "/topics/bank",
                    "/topics/add",
                    "/topics/edit/**",
                    "/paper/generate",
                    "/paper/check",
                    "/paper/upload",
                    "/paper-configs",
                    "/papers/duplicate-check",
                    "/papers/duplicate-check/**",
                    "/user/profile",
                    // 注意：/admin/** 不在这里排除，因为需要单独的管理员权限检查
                    "/auth/login",
                    "/login",
                    "/register",
                    "/login.html",
                    "/chat.html",
                    "/profile.html",
                    "/index.html",
                    "/diagnostic",
                    "/question/**",       // 排除出题页面路径
                    "/api/user/login",
                    "/api/user/register",
                    "/api/user/validate",
                    "/api/user/debug/**", // 排除用户调试API
                    "/api/admin/login",   // 排除管理员登录API
                    "/api/captcha/**",    // 排除验证码API
                    "/api/tac/**",        // 排除TIANAI验证码API
                    "/api/upload-stats/**", // 排除上传统计API（需要登录验证）
                    "/api/papers/**",     // 排除试卷下载API（需要登录验证）
                    "/api/books/search",  // 排除书籍搜索API
                    "/api/books",         // 排除获取所有书籍API
                    "/api/books/type/**", // 排除按类型获取书籍API
                    "/test/**",           // 排除测试页面
                    "/css/**",
                    "/js/**",
                    "/images/**",
                    "/static/**",
                    "/assets/**",        // 排除管理员后台静态资源
                    "/tac/**",           // 排除TIANAI验证码静态资源
                    "/favicon.ico",
                    "/error"
                );

        // 添加管理员后台拦截器 - 必须在认证拦截器之后
        registry.addInterceptor(adminInterceptor)
                .addPathPatterns("/admin/**")  // 拦截所有管理员后台路径
                .excludePathPatterns(
                    "/admin/login",           // 排除管理员登录页面
                    "/admin/register",        // 排除管理员注册页面
                    "/admin/access-denied",   // 排除访问拒绝页面
                    "/admin/static/**",       // 排除静态资源
                    "/admin/css/**",
                    "/admin/js/**",
                    "/admin/images/**",
                    "/admin/assets/**"        // 排除管理员后台静态资源
                );
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // Static resources - 添加缓存控制
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600)
                .resourceChain(true);

        // 兼容性：同时支持不带/static前缀的访问
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/")
                .setCachePeriod(3600);
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/")
                .setCachePeriod(3600);
        registry.addResourceHandler("/images/**")
                .addResourceLocations("classpath:/static/images/")
                .setCachePeriod(3600);

        // 管理员后台静态资源配置
        registry.addResourceHandler("/admin/assets/**")
                .addResourceLocations("classpath:/templates/admin/assets/")
                .setCachePeriod(3600);

        // 管理员后台资源的兼容性映射
        registry.addResourceHandler("/assets/**")
                .addResourceLocations("classpath:/templates/admin/assets/")
                .setCachePeriod(3600);

        // TIANAI-CAPTCHA 验证码资源
        registry.addResourceHandler("/tac/**")
                .addResourceLocations("classpath:/tac/")
                .setCachePeriod(3600);

        // Main resources
        registry.addResourceHandler("/main/**")
                .addResourceLocations("classpath:/main/");
        // Public resources
        registry.addResourceHandler("/public/**")
                .addResourceLocations("classpath:/public/");

        // Uploads
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:uploads/");
    }

    @Override
    public void addCorsMappings(@NonNull CorsRegistry registry) {
        // Global CORS configuration
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .exposedHeaders("Authorization")
                .allowCredentials(true)
                .maxAge(3600);

        // API-specific CORS configuration
        registry.addMapping("/api/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}