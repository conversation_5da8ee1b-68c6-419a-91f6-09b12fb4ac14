package com.edu.maizi_edu_sys.config;

import cloud.tianai.captcha.resource.ResourceStore;
import cloud.tianai.captcha.resource.common.model.dto.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

// import static cloud.tianai.captcha.generator.impl.StandardSliderImageCaptchaGenerator.DEFAULT_SLIDER_IMAGE_TEMPLATE_PATH;

/**
 * TIANAI-CAPTCHA 验证码资源配置
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TacCaptchaConfig {

    private final ResourceStore resourceStore;

    @PostConstruct
    public void init() {
        log.info("初始化TIANAI-CAPTCHA验证码资源配置");

        try {
            // 添加背景图片资源
            addBackgroundResources();

            log.info("TIANAI-CAPTCHA验证码资源配置完成");
        } catch (Exception e) {
            log.error("TIANAI-CAPTCHA验证码资源配置失败", e);
        }
    }



    /**
     * 添加背景图片资源（使用官方示例图片）
     */
    private void addBackgroundResources() {
        // 滑块验证码背景图片
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/a.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/b.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/c.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/d.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/e.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/g.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/h.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/i.jpg", "default"));
        resourceStore.addResource("SLIDER", new Resource("classpath", "static/images/bgimages/j.jpg", "default"));

        // 旋转验证码背景图片
        resourceStore.addResource("ROTATE", new Resource("classpath", "static/images/bgimages/48.jpg", "default"));

        // 滑动还原验证码背景图片
        resourceStore.addResource("CONCAT", new Resource("classpath", "static/images/bgimages/48.jpg", "default"));

        // 点选验证码背景图片
        resourceStore.addResource("WORD_IMAGE_CLICK", new Resource("classpath", "static/images/bgimages/c.jpg", "default"));

        log.debug("添加背景图片资源完成");
    }
}
