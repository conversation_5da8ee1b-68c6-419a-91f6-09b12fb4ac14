package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 管理员页面视图控制器
 */
@Controller
@RequestMapping("/admin")
public class AdminViewController {

    /**
     * 管理员首页
     */
    @GetMapping({"/", "/index"})
    public String adminIndex() {
        return "admin/index";
    }

    /**
     * 题目审核页面
     */
    @GetMapping("/topics/audit")
    public String topicAudit() {
        return "admin/topic-audit";
    }

    // 用户管理页面已移至 AdminDashboardController.userManagement()
    // 该方法包含完整的权限验证和错误处理逻辑

    /**
     * 系统统计页面
     */
    @GetMapping("/stats")
    public String systemStats() {
        return "admin/system-stats";
    }

    /**
     * 权限管理页面
     */
    @GetMapping("/permissions")
    public String permissionManagement() {
        return "admin/permission-management";
    }

    /**
     * 管理员登录页面（不需要管理员权限检查）
     */
    @GetMapping("/login")
    public String adminLogin() {
        return "admin/templates/login";
    }

    /**
     * 管理员注册页面（不需要管理员权限检查）
     */
    @GetMapping("/register")
    public String adminRegister() {
        return "admin/templates/register";
    }

    /**
     * 管理员后台主页（新版本）
     */
    @GetMapping("/dashboard-new")
    public String adminDashboardNew() {
        return "admin/templates/dashboard";
    }

    /**
     * 访问被拒绝页面（不需要管理员权限检查）
     */
    @GetMapping("/access-denied")
    public String accessDenied() {
        return "admin/templates/access-denied";
    }

    /**
     * 验证码测试页面（不需要管理员权限检查）
     */
    @GetMapping("/test-captcha")
    public String testCaptcha() {
        return "test-captcha";
    }
}
