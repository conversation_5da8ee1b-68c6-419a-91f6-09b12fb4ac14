package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.dto.LoginRequest;
import com.edu.maizi_edu_sys.entity.dto.AdminLoginRequest;
import com.edu.maizi_edu_sys.entity.dto.RegisterRequest;
import com.edu.maizi_edu_sys.mapper.TopicAuditMapper;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
public class AdminController {

    private final TopicAuditService topicAuditService;
    private final AuthService authService;
    private final PermissionService permissionService;
    private final UserService userService;
    private final HttpServletRequest request;

    /**
     * 管理员登录（支持TIANAI-CAPTCHA验证码）
     * 专门为管理员提供的登录接口，验证管理员权限
     */
    @PostMapping("/login")
    public ResponseEntity<com.edu.maizi_edu_sys.entity.dto.ApiResponse<?>> adminLogin(@Valid @RequestBody AdminLoginRequest adminLoginRequest) {
        // 获取客户端IP
        String clientIp = RequestUtil.getClientIp(request);
        adminLoginRequest.setIp(clientIp);
        log.info("管理员登录尝试，IP: {}, 用户名: {}", clientIp, adminLoginRequest.getUsername());

        // 验证TIANAI-CAPTCHA验证码token（如果提供了）
        if (adminLoginRequest.getCaptchaToken() != null && !adminLoginRequest.getCaptchaToken().isEmpty()) {
            // 这里可以添加额外的验证码token验证逻辑
            // 目前TIANAI-CAPTCHA已经在前端验证过了，这里主要是记录
            log.debug("收到验证码token: {}", adminLoginRequest.getCaptchaToken());
        }

        // 转换为标准登录请求
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername(adminLoginRequest.getUsername());
        loginRequest.setPassword(adminLoginRequest.getPassword());
        loginRequest.setIp(clientIp);
        // 对于管理员登录，我们跳过传统验证码验证，因为已经使用了TIANAI-CAPTCHA
        loginRequest.setSessionId("admin-skip-captcha");
        loginRequest.setCaptchaCode("admin-skip-captcha");

        // 调用用户服务进行登录验证
        com.edu.maizi_edu_sys.entity.dto.ApiResponse<?> loginResponse = userService.login(loginRequest);

        // 如果登录成功，需要验证是否为管理员
        if (loginResponse.getCode() == 200) {
            try {
                // 从响应中获取用户信息
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> data = (java.util.Map<String, Object>) loginResponse.getData();
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> userInfo = (java.util.Map<String, Object>) data.get("user");
                Integer role = (Integer) userInfo.get("role");

                // 检查是否为管理员（role = 1）
                if (role == null || role != 1) {
                    log.warn("非管理员用户尝试管理员登录，用户名: {}, 角色: {}", adminLoginRequest.getUsername(), role);
                    return ResponseEntity.badRequest()
                            .body(com.edu.maizi_edu_sys.entity.dto.ApiResponse.error("您没有管理员权限，无法访问管理后台"));
                }

                log.info("管理员登录成功，用户名: {}", adminLoginRequest.getUsername());
                return ResponseEntity.ok(loginResponse);

            } catch (Exception e) {
                log.error("管理员登录验证失败", e);
                return ResponseEntity.badRequest()
                        .body(com.edu.maizi_edu_sys.entity.dto.ApiResponse.error("登录验证失败"));
            }
        } else {
            log.warn("管理员登录失败，用户名: {}, 原因: {}", adminLoginRequest.getUsername(), loginResponse.getMessage());
            return ResponseEntity.badRequest().body(loginResponse);
        }
    }

    /**
     * 管理员注册（支持TIANAI-CAPTCHA验证码）
     * 注册新的管理员账号，需要验证码验证
     */
    @PostMapping("/register")
    public ResponseEntity<com.edu.maizi_edu_sys.entity.dto.ApiResponse<?>> adminRegister(@Valid @RequestBody RegisterRequest registerRequest) {
        // 获取客户端IP
        String clientIp = RequestUtil.getClientIp(request);
        log.info("管理员注册尝试，IP: {}, 用户名: {}", clientIp, registerRequest.getUsername());

        // 验证TIANAI-CAPTCHA验证码token（如果提供了）
        if (registerRequest.getCaptchaToken() != null && !registerRequest.getCaptchaToken().isEmpty()) {
            log.debug("收到管理员注册验证码token: {}", registerRequest.getCaptchaToken());
        }

        try {
            // 调用用户服务进行注册
            com.edu.maizi_edu_sys.entity.dto.ApiResponse<String> registerResponse = userService.register(registerRequest);

            if (registerResponse.getCode() == 200) {
                // 注册成功后，需要将用户角色设置为管理员
                // 这里可以添加额外的管理员权限设置逻辑
                log.info("管理员注册成功，用户名: {}", registerRequest.getUsername());
                return ResponseEntity.ok(com.edu.maizi_edu_sys.entity.dto.ApiResponse.success("管理员注册成功，请联系系统管理员激活账号"));
            } else {
                log.warn("管理员注册失败，用户名: {}, 原因: {}", registerRequest.getUsername(), registerResponse.getMessage());
                return ResponseEntity.badRequest().body(registerResponse);
            }

        } catch (Exception e) {
            log.error("管理员注册异常", e);
            return ResponseEntity.badRequest()
                    .body(com.edu.maizi_edu_sys.entity.dto.ApiResponse.error("注册失败"));
        }
    }

    /**
     * 获取审核列表
     */
    @GetMapping("/topics/audit")
    public ResponseEntity<ApiResponse<IPage<TopicAudit>>> getAuditList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) Integer auditStatus,
            @RequestParam(required = false) String keyword) {
        
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "TOPIC_AUDIT")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            IPage<TopicAudit> auditList = topicAuditService.getAuditList(pageNum, pageSize, auditStatus, keyword);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取审核列表成功", auditList));
        } catch (Exception e) {
            log.error("获取审核列表失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取审核列表失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取审核详情
     */
    @GetMapping("/topics/audit/{auditId}")
    public ResponseEntity<ApiResponse<TopicAudit>> getAuditDetail(@PathVariable Long auditId) {
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "TOPIC_AUDIT")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            TopicAudit audit = topicAuditService.getAuditDetail(auditId);
            if (audit == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(new ApiResponse<>(true, "获取审核详情成功", audit));
        } catch (Exception e) {
            log.error("获取审核详情失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取审核详情失败: " + e.getMessage(), null));
        }
    }

    /**
     * 审核通过
     */
    @PostMapping("/topics/audit/{auditId}/approve")
    public ResponseEntity<ApiResponse<Void>> approveAudit(
            @PathVariable Long auditId,
            @RequestBody @Valid AuditRequest request) {
        
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "TOPIC_AUDIT")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限操作", null));
            }
            
            topicAuditService.approveTopicAudit(auditId, currentUserId, request.getComment());
            return ResponseEntity.ok(new ApiResponse<>(true, "审核通过成功", null));
        } catch (Exception e) {
            log.error("审核通过失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "审核通过失败: " + e.getMessage(), null));
        }
    }

    /**
     * 审核拒绝
     */
    @PostMapping("/topics/audit/{auditId}/reject")
    public ResponseEntity<ApiResponse<Void>> rejectAudit(
            @PathVariable Long auditId,
            @RequestBody @Valid RejectRequest request) {
        
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "TOPIC_AUDIT")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限操作", null));
            }
            
            topicAuditService.rejectTopicAudit(auditId, currentUserId, request.getRejectReason());
            return ResponseEntity.ok(new ApiResponse<>(true, "审核拒绝成功", null));
        } catch (Exception e) {
            log.error("审核拒绝失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "审核拒绝失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取待审核数量
     */
    @GetMapping("/topics/audit/pending-count")
    public ResponseEntity<ApiResponse<Long>> getPendingAuditCount() {
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "TOPIC_AUDIT")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            Long count = topicAuditService.getPendingAuditCount();
            return ResponseEntity.ok(new ApiResponse<>(true, "获取待审核数量成功", count));
        } catch (Exception e) {
            log.error("获取待审核数量失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取待审核数量失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取用户审核统计
     */
    @GetMapping("/topics/audit/stats/{userId}")
    public ResponseEntity<ApiResponse<TopicAuditMapper.TopicAuditStats>> getUserAuditStats(@PathVariable Long userId) {
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "USER_MANAGE")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            TopicAuditMapper.TopicAuditStats stats = topicAuditService.getUserAuditStats(userId);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取用户审核统计成功", stats));
        } catch (Exception e) {
            log.error("获取用户审核统计失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取用户审核统计失败: " + e.getMessage(), null));
        }
    }

    /**
     * 审核请求DTO
     */
    public static class AuditRequest {
        private String comment;

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }
    }

    /**
     * 拒绝请求DTO
     */
    public static class RejectRequest {
        @NotBlank(message = "拒绝原因不能为空")
        private String rejectReason;

        public String getRejectReason() {
            return rejectReason;
        }

        public void setRejectReason(String rejectReason) {
            this.rejectReason = rejectReason;
        }
    }
}
