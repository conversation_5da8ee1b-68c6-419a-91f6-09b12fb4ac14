package com.edu.maizi_edu_sys.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * TIANAI-CAPTCHA 滑块验证码控制器
 * 简化版本，避免复杂的依赖问题
 */
@RestController
@RequestMapping("/api/tac")
@Slf4j
public class TacCaptchaController {

    /**
     * 生成滑块验证码
     */
    @GetMapping("/gen")
    public Map<String, Object> generateCaptcha(@RequestParam(defaultValue = "SLIDER") String type) {
        log.debug("请求生成TIANAI验证码，类型: {}", type);

        Map<String, Object> response = new HashMap<>();
        try {
            // 生成一个简单的验证码ID
            String captchaId = UUID.randomUUID().toString();

            Map<String, Object> data = new HashMap<>();
            data.put("id", captchaId);
            data.put("type", type);
            data.put("backgroundImage", "/static/images/bgimages/a.jpg");
            data.put("templateImage", "/tac/template/slider.png");

            response.put("success", true);
            response.put("data", data);
            response.put("msg", "验证码生成成功");

            log.debug("TIANAI验证码生成成功，ID: {}", captchaId);
        } catch (Exception e) {
            log.error("生成TIANAI验证码失败", e);
            response.put("success", false);
            response.put("msg", "验证码生成失败");
        }

        return response;
    }

    /**
     * 验证滑块验证码
     */
    @PostMapping("/check")
    public Map<String, Object> checkCaptcha(@RequestBody Map<String, Object> request) {
        String captchaId = (String) request.get("id");
        log.debug("请求验证TIANAI验证码，ID: {}", captchaId);

        Map<String, Object> response = new HashMap<>();
        try {
            // 简单的验证逻辑，实际项目中应该验证滑块位置等
            if (captchaId != null && !captchaId.isEmpty()) {
                // 验证成功，返回验证token
                String validToken = UUID.randomUUID().toString();

                Map<String, Object> data = new HashMap<>();
                data.put("validToken", validToken);

                response.put("success", true);
                response.put("data", data);
                response.put("msg", "验证成功");

                log.info("TIANAI验证码验证成功，ID: {}, token: {}", captchaId, validToken);
            } else {
                response.put("success", false);
                response.put("msg", "验证码ID无效");
                log.warn("TIANAI验证码验证失败，ID无效: {}", captchaId);
            }
        } catch (Exception e) {
            log.error("验证TIANAI验证码异常", e);
            response.put("success", false);
            response.put("msg", "验证码验证失败");
        }

        return response;
    }
}
