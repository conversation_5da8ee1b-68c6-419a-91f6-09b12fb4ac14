package com.edu.maizi_edu_sys.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TIANAI-CAPTCHA 验证码控制器测试
 */
@WebMvcTest(TacCaptchaController.class)
public class TacCaptchaControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testGenerateCaptcha() throws Exception {
        mockMvc.perform(get("/api/tac/gen"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").exists())
                .andExpect(jsonPath("$.data.type").value("SLIDER"));
    }

    @Test
    public void testGenerateCaptchaWithType() throws Exception {
        mockMvc.perform(get("/api/tac/gen?type=ROTATE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.type").value("ROTATE"));
    }

    @Test
    public void testCheckCaptcha() throws Exception {
        String requestBody = "{\"id\":\"test-id\",\"data\":{}}";
        
        mockMvc.perform(post("/api/tac/check")
                .contentType("application/json")
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.validToken").exists());
    }

    @Test
    public void testCheckCaptchaWithInvalidId() throws Exception {
        String requestBody = "{\"id\":\"\",\"data\":{}}";
        
        mockMvc.perform(post("/api/tac/check")
                .contentType("application/json")
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("验证码ID无效"));
    }
}
