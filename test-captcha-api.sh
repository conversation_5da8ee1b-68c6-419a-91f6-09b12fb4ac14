#!/bin/bash

# TIANAI-CAPTCHA API 测试脚本

echo "=== TIANAI-CAPTCHA API 测试 ==="
echo ""

# 测试验证码生成接口
echo "1. 测试验证码生成接口..."
echo "GET http://localhost:8080/api/tac/gen"
curl -X GET "http://localhost:8080/api/tac/gen" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  2>/dev/null || echo "连接失败 - 请确保应用程序正在运行"

echo ""
echo "---"
echo ""

# 测试验证码生成接口（指定类型）
echo "2. 测试验证码生成接口（旋转类型）..."
echo "GET http://localhost:8080/api/tac/gen?type=ROTATE"
curl -X GET "http://localhost:8080/api/tac/gen?type=ROTATE" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  2>/dev/null || echo "连接失败 - 请确保应用程序正在运行"

echo ""
echo "---"
echo ""

# 测试验证码验证接口
echo "3. 测试验证码验证接口..."
echo "POST http://localhost:8080/api/tac/check"
curl -X POST "http://localhost:8080/api/tac/check" \
  -H "Content-Type: application/json" \
  -d '{"id":"test-captcha-id","data":{}}' \
  -w "\nHTTP Status: %{http_code}\n" \
  2>/dev/null || echo "连接失败 - 请确保应用程序正在运行"

echo ""
echo "---"
echo ""

# 测试验证码验证接口（无效ID）
echo "4. 测试验证码验证接口（无效ID）..."
echo "POST http://localhost:8080/api/tac/check"
curl -X POST "http://localhost:8080/api/tac/check" \
  -H "Content-Type: application/json" \
  -d '{"id":"","data":{}}' \
  -w "\nHTTP Status: %{http_code}\n" \
  2>/dev/null || echo "连接失败 - 请确保应用程序正在运行"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果看到 HTTP Status: 200 和 JSON 响应，说明接口工作正常"
echo "如果看到连接失败，请先启动应用程序：java -jar target/maizi_edu_sys-*.jar"
echo ""
echo "访问测试页面："
echo "- 验证码测试页面: http://localhost:8080/admin/test-captcha"
echo "- 管理员登录页面: http://localhost:8080/admin/login"
echo "- 新版管理员后台: http://localhost:8080/admin/dashboard-new"
